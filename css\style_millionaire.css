/* Millionaire Quiz Game Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460);
    color: white;
    min-height: 100vh;
    overflow-x: hidden;
}

.game-container {
    width: 100%;
    min-height: 100vh;
    position: relative;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    justify-content: center;
    align-items: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background: linear-gradient(145deg, #2a2a4e, #1e1e3f);
    padding: 30px;
    border-radius: 20px;
    text-align: center;
    max-width: 500px;
    width: 90%;
    border: 2px solid #ffd700;
    box-shadow: 0 0 30px rgba(255, 215, 0, 0.3);
}

.modal h2, .modal h3 {
    color: #ffd700;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

/* Pokemon Host Styles */
.pokemon-host, .pokemon-host-game {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 20px;
}

.pikachu {
    position: relative;
    width: 150px;
    height: 150px;
}

.pikachu-body {
    width: 120px;
    height: 120px;
    background: linear-gradient(145deg, #ffdd44, #ffcc00);
    border-radius: 50%;
    position: relative;
    border: 4px solid #ff6b6b;
    box-shadow: 0 0 30px rgba(255, 221, 68, 0.6);
    margin: 15px auto;
}

.pikachu-face {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.pikachu-eye {
    width: 12px;
    height: 18px;
    background: #000;
    border-radius: 50%;
    display: inline-block;
    margin: 0 12px;
    animation: blink 3s infinite;
}

.pikachu-cheek {
    position: absolute;
    width: 18px;
    height: 18px;
    background: #ff6b6b;
    border-radius: 50%;
    top: 12px;
    animation: pulse 2s infinite;
}

.left-cheek {
    left: -30px;
}

.right-cheek {
    right: -30px;
}

.pikachu-mouth {
    width: 24px;
    height: 12px;
    border-radius: 0 0 24px 24px;
    margin: 12px auto;
}

.pikachu-mouth.happy {
    background: #ff6b6b;
    box-shadow: 0 0 8px #ff6b6b;
}

.pikachu-mouth.sad {
    background: #666;
    transform: rotate(180deg);
}

.pikachu-ear {
    position: absolute;
    width: 30px;
    height: 50px;
    background: linear-gradient(145deg, #ffdd44, #ffcc00);
    border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
    border: 3px solid #ff6b6b;
    top: -25px;
}

.left-ear {
    left: 10px;
    transform: rotate(-30deg);
}

.right-ear {
    right: 10px;
    transform: rotate(30deg);
}

.ear-tip {
    position: absolute;
    top: 3px;
    left: 50%;
    transform: translateX(-50%);
    width: 12px;
    height: 18px;
    background: #000;
    border-radius: 50%;
}

.pikachu-tail {
    position: absolute;
    right: -40px;
    top: 30px;
    width: 45px;
    height: 22px;
    background: linear-gradient(145deg, #ffdd44, #ffcc00);
    border-radius: 50%;
    border: 3px solid #ff6b6b;
    transform: rotate(45deg);
}

.pikachu-tail::before {
    content: '';
    position: absolute;
    right: -15px;
    top: -8px;
    width: 30px;
    height: 38px;
    background: linear-gradient(145deg, #ffdd44, #ffcc00);
    border-radius: 50%;
    border: 3px solid #ff6b6b;
    transform: rotate(-30deg);
}

.speech-bubble {
    background: white;
    color: #333;
    padding: 12px 18px;
    border-radius: 20px;
    position: relative;
    margin-top: 15px;
    font-size: 16px;
    font-weight: bold;
    box-shadow: 0 4px 15px rgba(0,0,0,0.3);
    min-width: 120px;
    text-align: center;
}

.speech-bubble::before {
    content: '';
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 12px solid transparent;
    border-right: 12px solid transparent;
    border-bottom: 12px solid white;
}

@keyframes blink {
    0%, 90%, 100% { opacity: 1; }
    95% { opacity: 0.3; }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.2); opacity: 0.7; }
}

@keyframes pikachuHappy {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(-3deg); }
    75% { transform: rotate(3deg); }
}

@keyframes pikachuSad {
    0%, 100% { transform: rotate(0deg); }
    50% { transform: rotate(-10deg); }
}

@keyframes bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

@keyframes goldGlow {
    0%, 100% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.3); }
    50% { box-shadow: 0 0 40px rgba(255, 215, 0, 0.8); }
}

@keyframes celebration {
    0% { transform: scale(1) rotate(0deg); }
    25% { transform: scale(1.1) rotate(-5deg); }
    50% { transform: scale(1.2) rotate(0deg); }
    75% { transform: scale(1.1) rotate(5deg); }
    100% { transform: scale(1) rotate(0deg); }
}

@keyframes confetti {
    0% { transform: translateY(-100vh) rotate(0deg); opacity: 1; }
    100% { transform: translateY(100vh) rotate(720deg); opacity: 0; }
}

/* Instructions */
.instructions ul {
    text-align: left;
    margin: 20px 0;
    padding-left: 20px;
}

.instructions li {
    margin: 8px 0;
    color: #e0e0e0;
}

/* Buttons */
button {
    background: linear-gradient(145deg, #ffd700, #ffed4e);
    color: #1a1a2e;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
}

button:disabled {
    background: #666;
    color: #999;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Game Area */
.game-area {
    display: none;
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;
}

.game-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    background: rgba(255, 255, 255, 0.1);
    padding: 15px;
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

.robot-host-game .robot-head {
    width: 60px;
    height: 60px;
}

.game-info {
    display: flex;
    gap: 30px;
    align-items: center;
}

.timer, .current-prize {
    background: rgba(255, 215, 0, 0.2);
    padding: 10px 20px;
    border-radius: 20px;
    font-size: 18px;
    font-weight: bold;
    border: 2px solid #ffd700;
}

/* New Game Content Layout */
.game-content-new {
    display: grid;
    grid-template-columns: 250px 1fr;
    gap: 30px;
    margin-bottom: 20px;
    align-items: start;
}

.pokemon-host-game {
    position: sticky;
    top: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

/* Horizontal Prize Ladder */
.prize-ladder-horizontal {
    background: rgba(255, 255, 255, 0.1);
    padding: 15px;
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 215, 0, 0.3);
    margin-bottom: 20px;
}

.prize-ladder-horizontal h3 {
    color: #ffd700;
    text-align: center;
    margin-bottom: 15px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    font-size: 18px;
}

.ladder-scroll-container {
    overflow-x: auto;
    overflow-y: hidden;
    padding: 10px 0;
    scrollbar-width: thin;
    scrollbar-color: #ffd700 rgba(255, 255, 255, 0.1);
}

.ladder-scroll-container::-webkit-scrollbar {
    height: 8px;
}

.ladder-scroll-container::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

.ladder-scroll-container::-webkit-scrollbar-thumb {
    background: #ffd700;
    border-radius: 4px;
}

.ladder-horizontal {
    display: flex;
    gap: 10px;
    min-width: max-content;
    padding: 5px;
}

.prize-item-horizontal {
    padding: 12px 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 25px;
    font-size: 14px;
    font-weight: bold;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    white-space: nowrap;
    min-width: 100px;
    text-align: center;
    cursor: pointer;
}

.prize-item-horizontal.current {
    background: linear-gradient(145deg, #ffd700, #ffed4e);
    color: #1a1a2e;
    transform: scale(1.1);
    border: 2px solid #fff;
    animation: goldGlow 2s infinite;
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.6);
}

.prize-item-horizontal.safe-point {
    border: 2px solid #00ff00;
    background: rgba(0, 255, 0, 0.2);
}

.prize-item-horizontal.completed {
    background: rgba(0, 255, 0, 0.3);
    border: 2px solid #00ff00;
    color: #fff;
}

.prize-item-horizontal:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

/* Question Area */
.question-area, .question-area-new {
    background: rgba(255, 255, 255, 0.1);
    padding: 30px;
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 215, 0, 0.3);
    flex: 1;
}

.question-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    font-size: 18px;
    color: #ffd700;
}

.question-text {
    font-size: 24px;
    margin-bottom: 30px;
    text-align: center;
    line-height: 1.4;
    color: #fff;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.options-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.option {
    background: rgba(255, 255, 255, 0.1);
    padding: 15px;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    display: flex;
    align-items: center;
}

.option:hover {
    background: rgba(255, 215, 0, 0.2);
    border-color: #ffd700;
    transform: translateY(-2px);
}

.option.selected {
    background: linear-gradient(145deg, #ffd700, #ffed4e);
    color: #1a1a2e;
    border-color: #fff;
}

.option.correct {
    background: linear-gradient(145deg, #00ff00, #32cd32);
    color: #000;
    border-color: #fff;
}

.option.incorrect {
    background: linear-gradient(145deg, #ff4444, #cc0000);
    color: #fff;
    border-color: #fff;
}

.option.eliminated {
    opacity: 0.3;
    pointer-events: none;
    background: #333;
}

.option-letter {
    font-weight: bold;
    margin-right: 10px;
    font-size: 18px;
}

.option-text {
    flex: 1;
    font-size: 16px;
}

/* Game Controls */
.game-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

.lifelines {
    display: flex;
    gap: 15px;
}

.lifeline {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px 15px;
    background: linear-gradient(145deg, #4a90e2, #357abd);
    color: white;
    min-width: 80px;
}

.lifeline:disabled {
    background: #666;
    opacity: 0.5;
}

.lifeline-icon {
    font-size: 20px;
    margin-bottom: 5px;
}

.lifeline-text {
    font-size: 12px;
}

.action-buttons {
    display: flex;
    gap: 15px;
}

.walk-away {
    background: linear-gradient(145deg, #ff6b6b, #cc0000);
    color: white;
}

.final-answer {
    background: linear-gradient(145deg, #00ff00, #32cd32);
    color: #000;
    font-size: 18px;
    padding: 15px 30px;
}

/* Audience Results */
.audience-results {
    margin: 20px 0;
}

.audience-bar {
    display: flex;
    align-items: center;
    margin: 10px 0;
    gap: 10px;
}

.option-label {
    font-weight: bold;
    width: 30px;
    color: #ffd700;
}

.bar-container {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 10px;
}

.bar {
    height: 25px;
    background: linear-gradient(145deg, #4a90e2, #357abd);
    border-radius: 12px;
    transition: width 1s ease;
    min-width: 2px;
}

.percentage {
    font-weight: bold;
    color: #ffd700;
    min-width: 40px;
}

/* Friend Advice */
.friend-advice {
    margin: 20px 0;
    padding: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    font-style: italic;
}

/* Result Content */
.result-content {
    margin: 20px 0;
}

.final-stats {
    background: rgba(255, 255, 255, 0.1);
    padding: 15px;
    border-radius: 10px;
    margin-top: 15px;
}

.final-stats p {
    margin: 5px 0;
    color: #e0e0e0;
}

.result-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 20px;
}

/* Celebration Effects */
.millionaire-celebration {
    animation: celebration 2s infinite;
}

.millionaire-celebration .pikachu {
    animation: bounce 1s infinite;
}

.millionaire-celebration .pikachu-body {
    animation: pikachuHappy 1s infinite;
}

.confetti {
    position: fixed;
    width: 10px;
    height: 10px;
    background: #ffd700;
    animation: confetti 3s linear infinite;
    z-index: 1001;
}

.confetti:nth-child(odd) {
    background: #ff6b6b;
    animation-delay: -0.5s;
}

.confetti:nth-child(even) {
    background: #4a90e2;
    animation-delay: -1s;
}

/* Animations */
@keyframes correctAnswer {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes incorrectAnswer {
    0% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
    100% { transform: translateX(0); }
}

.option.correct {
    animation: correctAnswer 0.6s ease;
}

.option.incorrect {
    animation: incorrectAnswer 0.6s ease;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .game-content-new {
        grid-template-columns: 220px 1fr;
        gap: 20px;
    }

    .pikachu {
        width: 120px;
        height: 120px;
    }

    .pikachu-body {
        width: 90px;
        height: 90px;
    }

    .pikachu-ear {
        width: 24px;
        height: 40px;
        top: -20px;
    }

    .pikachu-tail {
        right: -30px;
        top: 25px;
        width: 35px;
        height: 18px;
    }
}

@media (max-width: 768px) {
    .game-content-new {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .pokemon-host-game {
        order: 1;
        position: static;
        margin-bottom: 20px;
    }

    .question-area-new {
        order: 2;
    }

    .options-container {
        grid-template-columns: 1fr;
    }

    .game-controls {
        flex-direction: column;
        gap: 15px;
    }

    .lifelines {
        order: 2;
        justify-content: center;
    }

    .action-buttons {
        order: 1;
        justify-content: center;
    }

    .question-text {
        font-size: 20px;
    }

    .modal-content {
        padding: 20px;
        max-width: 95%;
    }

    .prize-ladder-horizontal {
        margin-bottom: 15px;
    }

    .ladder-horizontal {
        gap: 8px;
    }

    .prize-item-horizontal {
        min-width: 80px;
        padding: 10px 15px;
        font-size: 12px;
    }

    .pikachu {
        width: 100px;
        height: 100px;
    }

    .pikachu-body {
        width: 80px;
        height: 80px;
    }

    .pikachu-ear {
        width: 20px;
        height: 35px;
        top: -15px;
    }

    .pikachu-tail {
        right: -25px;
        top: 20px;
        width: 30px;
        height: 15px;
    }

    .speech-bubble {
        font-size: 14px;
        padding: 10px 14px;
    }
}

@media (max-width: 480px) {
    .prize-item-horizontal {
        min-width: 70px;
        padding: 8px 12px;
        font-size: 11px;
    }

    .ladder-horizontal {
        gap: 6px;
    }

    .game-header {
        flex-direction: column;
        gap: 10px;
    }

    .game-info {
        gap: 15px;
    }
}
