# Who Wants to Be a Millionaire - Robot Edition

A fully-featured millionaire quiz game with a cute robot host, integrated into your existing game platform.

## Features

### 🎮 Game Mechanics
- **15 Progressive Questions** with increasing difficulty and prize money
- **Prize Ladder**: $100 → $1,000,000
- **Safe Points**: Guaranteed money at $1,000 (Question 5) and $32,000 (Question 10)
- **Timer**: 30 seconds per question with visual countdown
- **Final Answer Confirmation**: Prevents accidental submissions

### 🎯 Lifelines (3 Available)
1. **50:50** - Eliminates two incorrect answers
2. **Ask the Audience** - Shows audience poll results with realistic percentages
3. **Phone a Friend** - Get advice from a virtual friend (80% accuracy)

### 🤖 Robot Host
- **Cute Circular Design** matching your avatar preferences
- **Animated Eyes** with blinking effects
- **Glowing Antennas** with pulsing animation
- **Emotional Expressions** (happy/sad) based on game outcome
- **Celebration Animations** for big wins

### 🎨 Visual Effects
- **Gradient Backgrounds** with space theme
- **Gold Highlighting** for current question value
- **Answer Animations** (correct/incorrect feedback)
- **Confetti Effect** for millionaire winners
- **Responsive Design** for all screen sizes

### 🔊 Audio Integration
- Uses existing sound files from your game system
- **Correct Answer** sound effects
- **Wrong Answer** feedback
- **Success** celebration sounds

## File Structure

```
html/
├── millionaire-quiz.html     # Main game page
css/
├── style_millionaire.css     # Game styling
js/
├── millionaire-quiz.js       # Game logic and mechanics
```

## Integration

The game is integrated into your main menu (`mainpage.html`) with a 💰 button that links to the millionaire quiz.

## Question Categories

### Easy Questions (1-5): $100 - $1,000
- Basic knowledge and common facts
- Simple math and geography

### Medium Questions (6-10): $2,000 - $32,000
- Art, history, and science
- More specific knowledge required

### Hard Questions (11-15): $64,000 - $1,000,000
- Advanced topics in science and technology
- Specialized knowledge areas

## Game Flow

1. **Welcome Screen** with robot host introduction
2. **Question Display** with 4 multiple choice options
3. **Lifeline Usage** (optional)
4. **Answer Selection** and confirmation
5. **Result Animation** with sound effects
6. **Prize Ladder Update** showing progress
7. **Final Results** with statistics and celebration

## Special Features

### Walk Away Option
Players can walk away at any time with their current winnings, adding strategic decision-making.

### Safe Points
Questions 5 and 10 are safe points - if you answer incorrectly after these, you keep the safe point money.

### Realistic Lifelines
- **Ask Audience**: Biased toward correct answer (40-70%) but not always certain
- **Phone Friend**: 80% chance of correct advice, 20% chance of uncertainty
- **50:50**: Always removes exactly 2 wrong answers

### Responsive Design
- Desktop: Side-by-side layout with prize ladder
- Mobile: Stacked layout with optimized touch controls
- Tablet: Adaptive layout for medium screens

## Technical Implementation

### JavaScript Classes
- Object-oriented design with `MillionaireQuiz` class
- Modular methods for each game feature
- Event-driven architecture

### CSS Animations
- Keyframe animations for robot expressions
- Smooth transitions for UI elements
- Visual feedback for user interactions

### Sound Integration
- Graceful fallback if audio files unavailable
- Volume control integration
- Multiple sound types for different events

## Future Enhancements

Potential additions for future versions:
- Question database expansion
- Difficulty level selection
- Multiplayer mode
- Achievement system integration
- Custom question categories
- Leaderboard functionality

## Browser Compatibility

- Modern browsers with ES6 support
- Chrome, Firefox, Safari, Edge
- Mobile browsers supported
- No external dependencies required

---

*Enjoy your millionaire quiz experience with your cute robot host!* 🤖💰
