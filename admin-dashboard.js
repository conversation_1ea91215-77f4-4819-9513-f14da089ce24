// Sample data - In real application, this would come from backend API
let users = [];
let questions = [];
let currentPage = 1;
let usersPerPage = 50;
let filteredUsers = [];

// Initialize sample data
function initializeData() {
    // Generate sample users
    for (let i = 1; i <= 150; i++) {
        users.push({
            id: i,
            username: `user${i}`,
            email: `user${i}@example.com`,
            password: '********', // In real app, never show actual passwords
            achievements: Math.floor(Math.random() * 50) + 1,
            totalAchievements: 50,
            levelsCompleted: Math.floor(Math.random() * 20) + 1,
            totalLevels: 20,
            tier: getTierFromExp(Math.floor(Math.random() * 10000)),
            exp: Math.floor(Math.random() * 10000),
            joinDate: new Date(2023, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1)
        });
    }

    // Generate sample questions
    const sampleQuestions = [
        {
            id: 1,
            question: "What is the capital of France?",
            options: ["London", "Berlin", "Paris", "Madrid"],
            correctAnswer: "C",
            difficulty: "Easy"
        },
        {
            id: 2,
            question: "Which programming language is known as the 'language of the web'?",
            options: ["Python", "JavaScript", "Java", "C++"],
            correctAnswer: "B",
            difficulty: "Medium"
        },
        {
            id: 3,
            question: "What is the time complexity of binary search?",
            options: ["O(n)", "O(log n)", "O(n²)", "O(1)"],
            correctAnswer: "B",
            difficulty: "Hard"
        }
    ];

    questions = [...sampleQuestions];
    filteredUsers = [...users];
}

function getTierFromExp(exp) {
    if (exp < 1000) return "Bronze";
    if (exp < 3000) return "Silver";
    if (exp < 6000) return "Gold";
    if (exp < 10000) return "Platinum";
    return "Diamond";
}

// Navigation functions
function showSection(sectionName) {
    // Hide all sections
    document.querySelectorAll('.content-section').forEach(section => {
        section.classList.remove('active');
    });

    // Remove active class from nav items
    document.querySelectorAll('.nav-item').forEach(item => {
        item.classList.remove('active');
    });

    // Show selected section
    document.getElementById(`${sectionName}-section`).classList.add('active');

    // Add active class to clicked nav item
    event.target.closest('.nav-item').classList.add('active');

    if (sectionName === 'users') {
        displayUsers();
    } else if (sectionName === 'content') {
        displayQuestions();
    }
}

// Users management functions
function displayUsers() {
    const startIndex = (currentPage - 1) * usersPerPage;
    const endIndex = startIndex + usersPerPage;
    const usersToShow = filteredUsers.slice(startIndex, endIndex);

    const usersGrid = document.getElementById('usersGrid');
    usersGrid.innerHTML = '';

    usersToShow.forEach(user => {
        const userCard = createUserCard(user);
        usersGrid.appendChild(userCard);
    });

    updatePagination();
}

function createUserCard(user) {
    const card = document.createElement('div');
    card.className = 'user-card';
    card.onclick = () => showUserDetails(user);

    card.innerHTML = `
        <div class="user-card-header">
            <div class="user-info">
                <h3>${user.username}</h3>
                <p>${user.email}</p>
            </div>
            <div class="user-actions">
                <button class="three-dots" onclick="toggleDropdown(event, ${user.id})">
                    <i class="fas fa-ellipsis-v"></i>
                </button>
                <div class="dropdown-menu" id="dropdown-${user.id}">
                    <a href="#" class="dropdown-item" onclick="showUserDetails(${JSON.stringify(user).replace(/"/g, '&quot;')})">
                        <i class="fas fa-eye"></i> View
                    </a>
                    <a href="#" class="dropdown-item" onclick="deleteUser(${user.id})">
                        <i class="fas fa-trash"></i> Delete
                    </a>
                </div>
            </div>
        </div>
        <div class="user-stats">
            <div class="stat">
                <div class="stat-value">${user.achievements}</div>
                <div class="stat-label">Achievements</div>
            </div>
            <div class="stat">
                <div class="stat-value">${user.levelsCompleted}</div>
                <div class="stat-label">Levels</div>
            </div>
            <div class="stat">
                <div class="stat-value">${user.tier}</div>
                <div class="stat-label">Tier</div>
            </div>
        </div>
    `;

    return card;
}

function toggleDropdown(event, userId) {
    event.stopPropagation();

    // Close all other dropdowns
    document.querySelectorAll('.dropdown-menu').forEach(menu => {
        if (menu.id !== `dropdown-${userId}`) {
            menu.classList.remove('show');
        }
    });

    // Toggle current dropdown
    const dropdown = document.getElementById(`dropdown-${userId}`);
    dropdown.classList.toggle('show');
}

function showUserDetails(user) {
    const modal = document.getElementById('userModal');
    const modalBody = document.getElementById('userModalBody');

    const achievementProgress = (user.achievements / user.totalAchievements) * 100;
    const levelProgress = (user.levelsCompleted / user.totalLevels) * 100;

    modalBody.innerHTML = `
        <div class="user-detail-section">
            <h3>Basic Information</h3>
            <div class="user-basic-info">
                <div class="info-item">
                    <div class="info-label">User ID</div>
                    <div class="info-value">${user.id}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Username</div>
                    <div class="info-value">${user.username}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Email</div>
                    <div class="info-value">${user.email}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Password</div>
                    <div class="info-value">${user.password}</div>
                </div>
            </div>
        </div>

        <div class="user-detail-section">
            <h3>Progress & Achievements</h3>
            <div class="progress-section">
                <div class="progress-item">
                    <div class="progress-header">
                        <span class="progress-label">Achievements Unlocked</span>
                        <span class="progress-value">${user.achievements}/${user.totalAchievements}</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${achievementProgress}%"></div>
                    </div>
                </div>

                <div class="progress-item">
                    <div class="progress-header">
                        <span class="progress-label">Levels Completed</span>
                        <span class="progress-value">${user.levelsCompleted}/${user.totalLevels}</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${levelProgress}%"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="user-detail-section">
            <h3>User Tier</h3>
            <div class="tier-display">
                <div class="tier-title">${user.tier} Tier</div>
                <div class="tier-exp">${user.exp} EXP</div>
            </div>
        </div>
    `;

    modal.classList.add('show');
}

function deleteUser(userId) {
    if (confirm('Are you sure you want to delete this user?')) {
        users = users.filter(user => user.id !== userId);
        filteredUsers = filteredUsers.filter(user => user.id !== userId);
        displayUsers();

        // Close dropdown
        document.getElementById(`dropdown-${userId}`).classList.remove('show');
    }
}

function searchUsers() {
    const searchTerm = document.getElementById('userSearch').value.toLowerCase();
    filteredUsers = users.filter(user =>
        user.username.toLowerCase().includes(searchTerm) ||
        user.email.toLowerCase().includes(searchTerm)
    );
    currentPage = 1;
    displayUsers();
}

function changePage(direction) {
    const totalPages = Math.ceil(filteredUsers.length / usersPerPage);
    const newPage = currentPage + direction;

    if (newPage >= 1 && newPage <= totalPages) {
        currentPage = newPage;
        displayUsers();
    }
}

function updatePagination() {
    const totalPages = Math.ceil(filteredUsers.length / usersPerPage);
    document.getElementById('pageInfo').textContent = `Page ${currentPage} of ${totalPages}`;
    document.getElementById('prevBtn').disabled = currentPage === 1;
    document.getElementById('nextBtn').disabled = currentPage === totalPages;
}

// Questions management functions
function displayQuestions() {
    const questionsContainer = document.getElementById('questionsContainer');
    questionsContainer.innerHTML = '';

    questions.forEach(question => {
        const questionCard = createQuestionCard(question);
        questionsContainer.appendChild(questionCard);
    });
}

function createQuestionCard(question) {
    const card = document.createElement('div');
    card.className = 'question-card';

    const optionLabels = ['A', 'B', 'C', 'D'];
    const optionsHtml = question.options.map((option, index) => {
        const isCorrect = optionLabels[index] === question.correctAnswer;
        return `
            <li>
                <span class="option-label">${optionLabels[index]}.</span>
                ${option}
                ${isCorrect ? '<span class="correct-answer">Correct</span>' : ''}
            </li>
        `;
    }).join('');

    card.innerHTML = `
        <div class="question-header">
            <div class="question-text">${question.question}</div>
            <div class="question-actions">
                <button class="btn-primary" onclick="editQuestion(${question.id})">
                    <i class="fas fa-edit"></i> Edit
                </button>
                <button class="btn-danger" onclick="deleteQuestion(${question.id})">
                    <i class="fas fa-trash"></i> Delete
                </button>
            </div>
        </div>
        <div class="question-meta">
            <span>Question ID: ${question.id}</span>
            <span class="difficulty-badge difficulty-${question.difficulty.toLowerCase()}">${question.difficulty}</span>
        </div>
        <ul class="options-list">
            ${optionsHtml}
        </ul>
    `;

    return card;
}

function showAddQuestionModal() {
    document.getElementById('questionModalTitle').textContent = 'Add Question';
    document.getElementById('questionForm').reset();
    document.getElementById('questionForm').onsubmit = handleAddQuestion;
    document.getElementById('questionModal').classList.add('show');
}

function editQuestion(questionId) {
    const question = questions.find(q => q.id === questionId);
    if (!question) return;

    document.getElementById('questionModalTitle').textContent = 'Edit Question';
    document.getElementById('questionText').value = question.question;
    document.getElementById('option1').value = question.options[0];
    document.getElementById('option2').value = question.options[1];
    document.getElementById('option3').value = question.options[2];
    document.getElementById('option4').value = question.options[3];
    document.getElementById('correctAnswer').value = question.correctAnswer;
    document.getElementById('difficulty').value = question.difficulty;

    document.getElementById('questionForm').onsubmit = (e) => handleEditQuestion(e, questionId);
    document.getElementById('questionModal').classList.add('show');
}

function handleAddQuestion(event) {
    event.preventDefault();

    const newQuestion = {
        id: Math.max(...questions.map(q => q.id), 0) + 1,
        question: document.getElementById('questionText').value,
        options: [
            document.getElementById('option1').value,
            document.getElementById('option2').value,
            document.getElementById('option3').value,
            document.getElementById('option4').value
        ],
        correctAnswer: document.getElementById('correctAnswer').value,
        difficulty: document.getElementById('difficulty').value
    };

    questions.push(newQuestion);
    displayQuestions();
    closeModal('questionModal');
}

function handleEditQuestion(event, questionId) {
    event.preventDefault();

    const questionIndex = questions.findIndex(q => q.id === questionId);
    if (questionIndex === -1) return;

    questions[questionIndex] = {
        ...questions[questionIndex],
        question: document.getElementById('questionText').value,
        options: [
            document.getElementById('option1').value,
            document.getElementById('option2').value,
            document.getElementById('option3').value,
            document.getElementById('option4').value
        ],
        correctAnswer: document.getElementById('correctAnswer').value,
        difficulty: document.getElementById('difficulty').value
    };

    displayQuestions();
    closeModal('questionModal');
}

function deleteQuestion(questionId) {
    if (confirm('Are you sure you want to delete this question?')) {
        questions = questions.filter(q => q.id !== questionId);
        displayQuestions();
    }
}

// Modal functions
function closeModal(modalId) {
    document.getElementById(modalId).classList.remove('show');
}

// Event listeners
document.addEventListener('DOMContentLoaded', function() {
    initializeData();
    displayUsers();

    // Close dropdowns when clicking outside
    document.addEventListener('click', function(event) {
        if (!event.target.closest('.user-actions')) {
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                menu.classList.remove('show');
            });
        }
    });

    // Close modals when clicking outside
    document.querySelectorAll('.modal').forEach(modal => {
        modal.addEventListener('click', function(event) {
            if (event.target === modal) {
                modal.classList.remove('show');
            }
        });
    });
});
