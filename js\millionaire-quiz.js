// Millionaire Quiz Game Logic
class MillionaireQuiz {
    constructor() {
        this.currentQuestion = 0;
        this.selectedAnswer = null;
        this.gameActive = false;
        this.timer = null;
        this.timeLeft = 30;
        this.lifelines = {
            fiftyFifty: true,
            askAudience: true,
            phoneFreind: true
        };
        this.lifelinesUsed = 0;
        
        // Prize amounts
        this.prizeAmounts = [
            100, 200, 300, 500, 1000,      // Questions 1-5
            2000, 4000, 8000, 16000, 32000, // Questions 6-10
            64000, 125000, 250000, 500000, 1000000 // Questions 11-15
        ];
        
        // Safe points (guaranteed money)
        this.safePoints = [4, 9]; // Questions 5 and 10 (0-indexed)
        
        this.initializeElements();
        this.initializeEventListeners();
        this.loadQuestions();
    }
    
    initializeElements() {
        // Modals
        this.instructionModal = document.getElementById('instruction-modal');
        this.gameArea = document.getElementById('game-area');
        this.resultsModal = document.getElementById('results-modal');
        this.audienceModal = document.getElementById('audience-modal');
        this.friendModal = document.getElementById('friend-modal');
        
        // Game elements
        this.questionText = document.getElementById('question-text');
        this.questionNum = document.getElementById('question-num');
        this.questionValue = document.getElementById('question-value');
        this.currentPrize = document.getElementById('current-prize');
        this.timeElement = document.getElementById('time');
        this.optionsContainer = document.getElementById('options-container');
        
        // Buttons
        this.startBtn = document.getElementById('start-btn');
        this.finalAnswerBtn = document.getElementById('final-answer-btn');
        this.walkAwayBtn = document.getElementById('walk-away-btn');
        this.playAgainBtn = document.getElementById('play-again-btn');
        this.backToMenuBtn = document.getElementById('back-to-menu-btn');
        
        // Lifelines
        this.fiftyFiftyBtn = document.getElementById('fifty-fifty');
        this.askAudienceBtn = document.getElementById('ask-audience');
        this.phoneFriendBtn = document.getElementById('phone-friend');
        
        // Results
        this.resultTitle = document.getElementById('result-title');
        this.resultMessage = document.getElementById('result-message');
        this.questionsAnswered = document.getElementById('questions-answered');
        this.lifelinesUsedSpan = document.getElementById('lifelines-used');
        this.resultFoxMouth = document.getElementById('result-fox-mouth');

        // Fox speech
        this.foxSpeech = document.getElementById('fox-speech');
    }
    
    initializeEventListeners() {
        this.startBtn.addEventListener('click', () => this.startGame());
        this.finalAnswerBtn.addEventListener('click', () => this.submitFinalAnswer());
        this.walkAwayBtn.addEventListener('click', () => this.walkAway());
        this.playAgainBtn.addEventListener('click', () => this.resetGame());
        this.backToMenuBtn.addEventListener('click', () => this.backToMenu());
        
        // Lifelines
        this.fiftyFiftyBtn.addEventListener('click', () => this.useFiftyFifty());
        this.askAudienceBtn.addEventListener('click', () => this.useAskAudience());
        this.phoneFriendBtn.addEventListener('click', () => this.usePhoneFriend());
        
        // Modal close buttons
        document.getElementById('close-audience-modal').addEventListener('click', () => {
            this.audienceModal.classList.remove('show');
        });
        document.getElementById('close-friend-modal').addEventListener('click', () => {
            this.friendModal.classList.remove('show');
        });
        
        // Option selection
        this.optionsContainer.addEventListener('click', (e) => {
            if (e.target.closest('.option') && !e.target.closest('.option').classList.contains('eliminated')) {
                this.selectOption(e.target.closest('.option'));
            }
        });
    }
    
    loadQuestions() {
        // Questions arranged by difficulty (easy to hard)
        this.questions = [
            // Questions 1-5: Easy ($100-$1,000)
            {
                question: "What is the capital of France?",
                options: ["London", "Berlin", "Paris", "Madrid"],
                correct: 2
            },
            {
                question: "Which planet is known as the Red Planet?",
                options: ["Venus", "Mars", "Jupiter", "Saturn"],
                correct: 1
            },
            {
                question: "What is 2 + 2?",
                options: ["3", "4", "5", "6"],
                correct: 1
            },
            {
                question: "Which animal is known as the 'King of the Jungle'?",
                options: ["Tiger", "Elephant", "Lion", "Leopard"],
                correct: 2
            },
            {
                question: "How many days are there in a week?",
                options: ["5", "6", "7", "8"],
                correct: 2
            },

            // Questions 6-10: Medium ($2,000-$32,000)
            {
                question: "Who painted the Mona Lisa?",
                options: ["Vincent van Gogh", "Pablo Picasso", "Leonardo da Vinci", "Michelangelo"],
                correct: 2
            },
            {
                question: "What is the chemical symbol for gold?",
                options: ["Go", "Gd", "Au", "Ag"],
                correct: 2
            },
            {
                question: "Which country has the most natural lakes?",
                options: ["Russia", "Canada", "Finland", "Sweden"],
                correct: 1
            },
            {
                question: "In which year did the Titanic sink?",
                options: ["1910", "1912", "1914", "1916"],
                correct: 1
            },
            {
                question: "What is the smallest prime number?",
                options: ["0", "1", "2", "3"],
                correct: 2
            },

            // Questions 11-15: Hard ($64,000-$1,000,000)
            {
                question: "Which programming language was created by Guido van Rossum?",
                options: ["Java", "Python", "C++", "JavaScript"],
                correct: 1
            },
            {
                question: "What is the speed of light in vacuum (approximately)?",
                options: ["299,792,458 m/s", "300,000,000 m/s", "299,000,000 m/s", "298,792,458 m/s"],
                correct: 0
            },
            {
                question: "Which element has the highest melting point?",
                options: ["Carbon", "Tungsten", "Rhenium", "Osmium"],
                correct: 1
            },
            {
                question: "In quantum mechanics, what principle states that you cannot simultaneously know both position and momentum of a particle?",
                options: ["Pauli Exclusion Principle", "Heisenberg Uncertainty Principle", "Schrödinger's Principle", "Planck's Principle"],
                correct: 1
            },
            {
                question: "What is the name of the theoretical boundary around a black hole beyond which nothing can escape?",
                options: ["Photon Sphere", "Ergosphere", "Event Horizon", "Singularity"],
                correct: 2
            }
        ];
    }
    
    startGame() {
        this.instructionModal.classList.remove('show');
        this.gameArea.style.display = 'block';
        this.gameActive = true;
        this.currentQuestion = 0;
        this.selectedAnswer = null;
        this.lifelinesUsed = 0;

        // Reset lifelines
        this.lifelines = {
            fiftyFifty: true,
            askAudience: true,
            phoneFreind: true
        };

        this.updateLifelineButtons();
        this.displayQuestion();
        this.updatePrizeLadder();
        this.startTimer();
    }
    
    displayQuestion() {
        if (this.currentQuestion >= this.questions.length) {
            this.endGame(true, this.prizeAmounts[this.currentQuestion - 1]);
            return;
        }

        const question = this.questions[this.currentQuestion];
        this.questionText.textContent = question.question;
        this.questionNum.textContent = this.currentQuestion + 1;
        this.questionValue.textContent = this.prizeAmounts[this.currentQuestion].toLocaleString();

        // Update Fox speech based on question difficulty
        const questionLevel = this.currentQuestion + 1;
        let speech = "Good luck!";
        if (questionLevel <= 5) {
            speech = "You can do it!";
        } else if (questionLevel <= 10) {
            speech = "Think carefully!";
        } else if (questionLevel <= 14) {
            speech = "This is tough!";
        } else {
            speech = "Final question!";
        }
        this.updateFoxSpeech(speech);

        // Update options
        const options = this.optionsContainer.querySelectorAll('.option');
        options.forEach((option, index) => {
            const optionText = option.querySelector('.option-text');
            optionText.textContent = question.options[index];
            option.classList.remove('selected', 'correct', 'incorrect', 'eliminated');
        });

        this.selectedAnswer = null;
        this.finalAnswerBtn.disabled = true;
        this.updatePrizeLadder();
    }
    
    selectOption(optionElement) {
        // Remove previous selection
        this.optionsContainer.querySelectorAll('.option').forEach(opt => {
            opt.classList.remove('selected');
        });
        
        // Select new option
        optionElement.classList.add('selected');
        this.selectedAnswer = Array.from(this.optionsContainer.querySelectorAll('.option')).indexOf(optionElement);
        this.finalAnswerBtn.disabled = false;
    }
    
    startTimer() {
        this.timeLeft = 30;
        this.timeElement.textContent = this.timeLeft;
        
        this.timer = setInterval(() => {
            this.timeLeft--;
            this.timeElement.textContent = this.timeLeft;
            
            if (this.timeLeft <= 0) {
                this.timeUp();
            }
        }, 1000);
    }
    
    stopTimer() {
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }
    }
    
    timeUp() {
        this.stopTimer();
        if (this.selectedAnswer === null) {
            this.endGame(false, this.getGuaranteedMoney());
        } else {
            this.submitFinalAnswer();
        }
    }
    
    submitFinalAnswer() {
        if (this.selectedAnswer === null) return;

        // Confirmation dialog
        const selectedOption = this.optionsContainer.querySelectorAll('.option')[this.selectedAnswer];
        const selectedText = selectedOption.querySelector('.option-text').textContent;
        const optionLetter = selectedOption.querySelector('.option-letter').textContent;

        if (!confirm(`Are you sure you want to lock in ${optionLetter} ${selectedText} as your final answer?`)) {
            return;
        }

        this.stopTimer();
        this.gameActive = false;

        const question = this.questions[this.currentQuestion];
        const isCorrect = this.selectedAnswer === question.correct;

        // Disable all options during reveal
        const options = this.optionsContainer.querySelectorAll('.option');
        options.forEach(option => option.style.pointerEvents = 'none');

        // Dramatic pause before revealing answer
        setTimeout(() => {
            // Show correct answer
            options[question.correct].classList.add('correct');

            if (isCorrect) {
                this.playSound('correct');
                // Flash the correct answer
                let flashCount = 0;
                const flashInterval = setInterval(() => {
                    options[question.correct].style.opacity = flashCount % 2 === 0 ? '0.5' : '1';
                    flashCount++;
                    if (flashCount >= 6) {
                        clearInterval(flashInterval);
                        options[question.correct].style.opacity = '1';
                    }
                }, 200);
            } else {
                options[this.selectedAnswer].classList.add('incorrect');
                this.playSound('wrong');
            }

            setTimeout(() => {
                if (isCorrect) {
                    this.currentQuestion++;
                    if (this.currentQuestion >= this.questions.length) {
                        // Won the game!
                        this.playSound('success');
                        this.endGame(true, 1000000);
                    } else {
                        // Check if reached a milestone
                        if (this.safePoints.includes(this.currentQuestion - 1)) {
                            alert(`Congratulations! You've reached a safe point with $${this.prizeAmounts[this.currentQuestion - 1].toLocaleString()}!`);
                        }

                        this.gameActive = true;
                        options.forEach(option => option.style.pointerEvents = 'auto');
                        this.displayQuestion();
                        this.startTimer();
                    }
                } else {
                    // Wrong answer - game over
                    this.endGame(false, this.getGuaranteedMoney());
                }
            }, 3000);
        }, 1000);
    }
    
    getGuaranteedMoney() {
        let guaranteed = 0;
        for (let i = this.safePoints.length - 1; i >= 0; i--) {
            if (this.currentQuestion > this.safePoints[i]) {
                guaranteed = this.prizeAmounts[this.safePoints[i]];
                break;
            }
        }
        return guaranteed;
    }
    
    walkAway() {
        const currentWinnings = this.currentQuestion > 0 ? this.prizeAmounts[this.currentQuestion - 1] : 0;
        this.endGame(true, currentWinnings, true);
    }
    
    updatePrizeLadder() {
        // Update horizontal prize ladder
        const prizeItems = document.querySelectorAll('.prize-item-horizontal');
        prizeItems.forEach((item, index) => {
            const level = parseInt(item.dataset.level);
            item.classList.remove('current', 'completed');

            if (level === this.currentQuestion + 1) {
                item.classList.add('current');
                // Scroll to current item
                item.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' });
            } else if (level <= this.currentQuestion) {
                item.classList.add('completed');
            }
        });

        // Update current prize display
        const currentWinnings = this.currentQuestion > 0 ? this.prizeAmounts[this.currentQuestion - 1] : 0;
        this.currentPrize.textContent = currentWinnings.toLocaleString();
    }

    updateFoxSpeech(message) {
        if (this.foxSpeech) {
            this.foxSpeech.textContent = message;
        }
    }

    // Lifeline Methods
    useFiftyFifty() {
        if (!this.lifelines.fiftyFifty || !this.gameActive) return;

        this.lifelines.fiftyFifty = false;
        this.lifelinesUsed++;
        this.updateLifelineButtons();
        this.updateFoxSpeech("Two wrong answers eliminated!");

        const question = this.questions[this.currentQuestion];
        const options = this.optionsContainer.querySelectorAll('.option');
        const incorrectOptions = [];

        // Find incorrect options
        options.forEach((option, index) => {
            if (index !== question.correct) {
                incorrectOptions.push(index);
            }
        });

        // Randomly eliminate 2 incorrect options
        const toEliminate = this.shuffleArray(incorrectOptions).slice(0, 2);
        toEliminate.forEach(index => {
            options[index].classList.add('eliminated');
        });

        this.playSound('correct'); // Use existing sound
    }

    useAskAudience() {
        if (!this.lifelines.askAudience || !this.gameActive) return;

        this.lifelines.askAudience = false;
        this.lifelinesUsed++;
        this.updateLifelineButtons();
        this.updateFoxSpeech("Let's see what the audience thinks!");

        const question = this.questions[this.currentQuestion];
        const audienceBars = this.audienceModal.querySelectorAll('.audience-bar');

        // Generate audience percentages (biased toward correct answer)
        const percentages = this.generateAudiencePercentages(question.correct);

        audienceBars.forEach((bar, index) => {
            const barElement = bar.querySelector('.bar');
            const percentageElement = bar.querySelector('.percentage');

            setTimeout(() => {
                barElement.style.width = percentages[index] + '%';
                percentageElement.textContent = percentages[index] + '%';
            }, index * 200);
        });

        this.audienceModal.classList.add('show');
        this.playSound('correct');
    }

    usePhoneFriend() {
        if (!this.lifelines.phoneFreind || !this.gameActive) return;

        this.lifelines.phoneFreind = false;
        this.lifelinesUsed++;
        this.updateLifelineButtons();
        this.updateFoxSpeech("Calling your friend for help!");

        const question = this.questions[this.currentQuestion];
        const friendMessage = document.getElementById('friend-message');

        // Generate friend's advice (usually helpful but not always certain)
        const advice = this.generateFriendAdvice(question);

        friendMessage.textContent = "Your friend is thinking...";
        this.friendModal.classList.add('show');

        setTimeout(() => {
            friendMessage.textContent = advice;
        }, 2000);

        this.playSound('correct');
    }

    generateAudiencePercentages(correctIndex) {
        const percentages = [0, 0, 0, 0];

        // Give correct answer 40-70% of votes
        const correctPercentage = 40 + Math.random() * 30;
        percentages[correctIndex] = Math.round(correctPercentage);

        // Distribute remaining percentage among other options
        let remaining = 100 - percentages[correctIndex];
        for (let i = 0; i < 4; i++) {
            if (i !== correctIndex) {
                if (remaining > 0) {
                    const percentage = Math.random() * remaining;
                    percentages[i] = Math.round(percentage);
                    remaining -= percentages[i];
                }
            }
        }

        // Adjust for rounding errors
        const total = percentages.reduce((sum, p) => sum + p, 0);
        if (total !== 100) {
            percentages[correctIndex] += (100 - total);
        }

        return percentages;
    }

    generateFriendAdvice(question) {
        const options = ['A', 'B', 'C', 'D'];
        const correctOption = options[question.correct];

        const adviceTemplates = [
            `I'm pretty sure it's ${correctOption}. That sounds right to me.`,
            `Hmm, I think it might be ${correctOption}, but I'm not 100% certain.`,
            `I remember learning about this - I believe it's ${correctOption}.`,
            `My gut feeling says ${correctOption}. Hope that helps!`,
            `I'd go with ${correctOption} if I were you.`
        ];

        // 80% chance of giving correct advice
        if (Math.random() < 0.8) {
            return adviceTemplates[Math.floor(Math.random() * adviceTemplates.length)];
        } else {
            // 20% chance of being uncertain or giving wrong advice
            const wrongOption = options[Math.floor(Math.random() * 4)];
            return `I'm not really sure about this one. Maybe ${wrongOption}? Sorry, I wish I could be more help.`;
        }
    }

    updateLifelineButtons() {
        this.fiftyFiftyBtn.disabled = !this.lifelines.fiftyFifty;
        this.askAudienceBtn.disabled = !this.lifelines.askAudience;
        this.phoneFriendBtn.disabled = !this.lifelines.phoneFreind;
    }

    endGame(won, winnings, walkedAway = false) {
        this.stopTimer();
        this.gameActive = false;

        // Update result display
        if (walkedAway) {
            this.resultTitle.textContent = "You Walked Away!";
            this.resultMessage.textContent = `You walked away with $${winnings.toLocaleString()}!`;
            this.resultFoxMouth.className = 'fox-mouth happy';
        } else if (won && winnings === 1000000) {
            this.resultTitle.textContent = "MILLIONAIRE!";
            this.resultMessage.textContent = "Congratulations! You won $1,000,000!";
            this.resultFoxMouth.className = 'fox-mouth happy';

            // Add celebration effects for millionaire win
            setTimeout(() => {
                this.resultsModal.querySelector('.modal-content').classList.add('millionaire-celebration');
                this.createConfetti();
            }, 1500);
        } else if (won) {
            this.resultTitle.textContent = "Congratulations!";
            this.resultMessage.textContent = `You won $${winnings.toLocaleString()}!`;
            this.resultFoxMouth.className = 'fox-mouth happy';
        } else {
            this.resultTitle.textContent = "Game Over!";
            this.resultMessage.textContent = `You leave with $${winnings.toLocaleString()}.`;
            this.resultFoxMouth.className = 'fox-mouth sad';
        }

        this.questionsAnswered.textContent = this.currentQuestion;
        this.lifelinesUsedSpan.textContent = this.lifelinesUsed;

        setTimeout(() => {
            this.resultsModal.classList.add('show');
        }, 1000);
    }

    createConfetti() {
        for (let i = 0; i < 50; i++) {
            setTimeout(() => {
                const confetti = document.createElement('div');
                confetti.className = 'confetti';
                confetti.style.left = Math.random() * 100 + 'vw';
                confetti.style.animationDelay = Math.random() * 3 + 's';
                document.body.appendChild(confetti);

                // Remove confetti after animation
                setTimeout(() => {
                    if (confetti.parentNode) {
                        confetti.parentNode.removeChild(confetti);
                    }
                }, 3000);
            }, i * 100);
        }
    }

    resetGame() {
        this.resultsModal.classList.remove('show');
        this.gameArea.style.display = 'none';
        this.instructionModal.classList.add('show');

        // Reset all game state
        this.currentQuestion = 0;
        this.selectedAnswer = null;
        this.gameActive = false;
        this.lifelinesUsed = 0;
        this.lifelines = {
            fiftyFifty: true,
            askAudience: true,
            phoneFreind: true
        };

        this.stopTimer();
    }

    backToMenu() {
        window.location.href = 'mainpage.html';
    }

    // Utility methods
    shuffleArray(array) {
        const shuffled = [...array];
        for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled;
    }

    playSound(type) {
        // Use existing sound files if available
        try {
            let audio;
            switch(type) {
                case 'correct':
                    audio = new Audio('/sounds/correct.mp3');
                    break;
                case 'wrong':
                    audio = new Audio('/sounds/wrong.mp3');
                    break;
                case 'success':
                    audio = new Audio('/sounds/success.mp3');
                    break;
                default:
                    return;
            }
            audio.volume = 0.3;
            audio.play().catch(e => console.log('Audio play failed:', e));
        } catch (e) {
            console.log('Audio not available:', e);
        }
    }
}

// Initialize the game when the page loads
document.addEventListener('DOMContentLoaded', () => {
    const game = new MillionaireQuiz();
    // Show the instruction modal initially
    game.instructionModal.classList.add('show');
});
